#!/usr/bin/env pwsh
# Script to fix Moq expression tree issues with optional arguments in VocomNativeInteropTests.cs

$filePath = "VolvoFlashWR.Communication.Tests\Vocom\VocomNativeInteropTests.cs"
$content = Get-Content -Path $filePath -Raw

# Pattern to find: _mockLogger.Verify(l => l.<PERSON>("message", "source"), Times.Once);
# Replace with: _mockLogger.Verify(l => l.<PERSON>(It.Is<string>(s => s == "message"), It.Is<string>(s => s == "source")), Times.Once);

$pattern = '_mockLogger\.Verify\(l => l\.LogError\("([^"]+)", "([^"]+)"\), Times\.Once\);'
$replacement = '_mockLogger.Verify(l => l.Log<PERSON>rror(It.Is<string>(s => s == "$1"), It.Is<string>(s => s == "$2")), Times.Once);'

$newContent = $content -replace $pattern, $replacement

# Write the updated content back to the file
Set-Content -Path $filePath -Value $newContent

Write-Host "Fixed Moq expression tree issues in $filePath"
