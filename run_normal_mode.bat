@echo off
echo Starting VolvoFlashWR application in Normal Mode...

REM Clear any existing environment variables first
set "USE_DUMMY_IMPLEMENTATIONS="
set "VERBOSE_LOGGING="
set "LOG_LEVEL="
set "SAFE_MODE="
set "DEMO_MODE="

REM Set environment variables for normal mode
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true

REM Set Phoenix Diag path
set PHOENIX_DIAG_PATH=C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021

REM Check if Phoenix Diag path exists
if not exist "%PHOENIX_DIAG_PATH%" (
    echo WARNING: Phoenix Diag path not found at %PHOENIX_DIAG_PATH%
    echo The application may not be able to connect to the Vocom adapter.
    pause
)

REM Check for specific required libraries
set REQUIRED_LIBS=0
if exist "%PHOENIX_DIAG_PATH%\apci.dll" set /a REQUIRED_LIBS+=1
if exist "%PHOENIX_DIAG_PATH%\Volvo.ApciPlus.dll" set /a REQUIRED_LIBS+=1
if exist "%PHOENIX_DIAG_PATH%\Ionic.Zip.Reduced.dll" set /a REQUIRED_LIBS+=1
if exist "%PHOENIX_DIAG_PATH%\SharpCompress.dll" set /a REQUIRED_LIBS+=1
if exist "%PHOENIX_DIAG_PATH%\Vodia.Contracts.Common.dll" set /a REQUIRED_LIBS+=1

if %REQUIRED_LIBS% LSS 5 (
    echo WARNING: Some required libraries are missing from %PHOENIX_DIAG_PATH%
    echo The application may not be able to connect to the Vocom adapter properly.
    echo Found %REQUIRED_LIBS% out of 5 required libraries.
    pause
)

REM Build the solution first
echo Building...
dotnet build VolvoFlashWR.sln --configuration Debug

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo The build failed. Fix the build errors and run again.
    pause
    exit /b %ERRORLEVEL%
)

REM Run the application from the build output with explicit mode
echo Running application in normal mode...
start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\VolvoFlashWR.Launcher.exe" --mode=Normal

echo Application should be running. Check for a new window that has opened.
pause
