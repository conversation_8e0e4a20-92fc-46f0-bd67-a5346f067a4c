@echo off
echo Starting VolvoFlashWR application in Normal Mode...

REM Clear any existing environment variables first
set "USE_DUMMY_IMPLEMENTATIONS="
set "VERBOSE_LOGGING="
set "LOG_LEVEL="
set "SAFE_MODE="
set "DEMO_MODE="

REM Set environment variables for normal mode
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true

REM Set Phoenix Diag path - skip library checks for now
echo Skipping Phoenix Diag library checks for this test run...

REM Build the solution first
echo Building...
dotnet build VolvoFlashWR.sln --configuration Debug

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo The build failed. Fix the build errors and run again.
    pause
    exit /b %ERRORLEVEL%
)

REM Run the application from the build output with explicit mode
echo Running application in normal mode...
start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" --mode=Normal

echo Application should be running. Check for a new window that has opened.
pause
