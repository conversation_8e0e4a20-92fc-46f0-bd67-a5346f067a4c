{"Application": {"UseDummyImplementations": false, "OperatingMode": "Normal", "ShowSplashScreen": true, "CheckForUpdates": true, "AutoConnectVocom": true, "AutoScanForECUs": true, "DefaultBackupLocation": "./Backups", "EnableDetailedLogging": true}, "Vocom": {"AutoDetect": true, "PreferredConnectionType": "USB", "USBTimeoutMs": 5000, "RetryCount": 3, "EnableTracing": true, "CustomDriverPath": "", "DisconnectPTTBeforeConnect": true}, "ECU": {"DefaultProtocol": "CAN", "TimeoutMs": 10000, "RetryCount": 3, "EnableSecurityAccess": true, "EnableMemoryProtection": true, "EnableECCSupport": true}, "Backup": {"AutoBackupEnabled": true, "BackupBeforeFlash": true, "BackupAfterFlash": true, "MaxBackupsToKeep": 10, "CompressionEnabled": true, "EncryptionEnabled": false}, "Scheduler": {"Enabled": true, "CheckIntervalMinutes": 15, "RunMissedBackups": true, "NotifyOnCompletion": true}, "UI": {"Theme": "Light", "AccentColor": "Blue", "ShowAdvancedOptions": true, "ShowDiagnosticInfo": true, "AutoRefreshInterval": 5}, "Logging": {"LogToConsole": true, "LogToFile": true, "MinimumLogLevel": "Debug"}, "Application.UseDummyImplementations": false}