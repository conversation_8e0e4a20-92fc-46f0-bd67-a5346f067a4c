using System;
using System.IO;
using System.Reflection;

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Testing assembly for patched implementation...");
        
        try
        {
            // Path to the assembly
            string assemblyPath = Path.Combine(Directory.GetCurrentDirectory(), "VolvoFlashWR.Communication.dll");
            if (!File.Exists(assemblyPath))
            {
                assemblyPath = Path.Combine(Directory.GetCurrentDirectory(), "VolvoFlashWR.Communication\\bin\\Debug\\net8.0\\VolvoFlashWR.Communication.dll");
            }
            
            if (!File.Exists(assemblyPath))
            {
                assemblyPath = Path.Combine(Directory.GetCurrentDirectory(), "VolvoFlashWR.Launcher\\bin\\Debug\\net8.0-windows\\win-x64\\VolvoFlashWR.Communication.dll");
            }
            
            Console.WriteLine($"Looking for assembly at: {assemblyPath}");
            
            if (!File.Exists(assemblyPath))
            {
                Console.WriteLine("Assembly not found!");
                return;
            }
            
            // Load the assembly
            Assembly assembly = Assembly.LoadFrom(assemblyPath);
            Console.WriteLine($"Loaded assembly: {assembly.FullName}");
            
            // Get all types in the assembly
            Type[] types = assembly.GetTypes();
            Console.WriteLine($"Found {types.Length} types in the assembly");
            
            // Look for patched implementation types
            foreach (Type type in types)
            {
                if (type.Name.Contains("Patch") || type.Name.Contains("patch"))
                {
                    Console.WriteLine($"Found patched type: {type.FullName}");
                }
            }
            
            // Look for specific types
            Type patchedFactoryType = assembly.GetType("VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory");
            if (patchedFactoryType != null)
            {
                Console.WriteLine($"Found PatchedVocomServiceFactory: {patchedFactoryType.FullName}");
            }
            else
            {
                Console.WriteLine("PatchedVocomServiceFactory not found!");
            }
            
            Type patchedDriverType = assembly.GetType("VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver");
            if (patchedDriverType != null)
            {
                Console.WriteLine($"Found PatchedVocomDeviceDriver: {patchedDriverType.FullName}");
            }
            else
            {
                Console.WriteLine("PatchedVocomDeviceDriver not found!");
            }
            
            Type patchedInteropType = assembly.GetType("VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch");
            if (patchedInteropType != null)
            {
                Console.WriteLine($"Found VocomNativeInterop_Patch: {patchedInteropType.FullName}");
            }
            else
            {
                Console.WriteLine("VocomNativeInterop_Patch not found!");
            }
            
            Type dllAnalyzerType = assembly.GetType("VolvoFlashWR.Communication.Vocom.DllAnalyzer");
            if (dllAnalyzerType != null)
            {
                Console.WriteLine($"Found DllAnalyzer: {dllAnalyzerType.FullName}");
            }
            else
            {
                Console.WriteLine("DllAnalyzer not found!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
