using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using VolvoFlashWR.Communication.Interfaces;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Tests.Microcontroller
{
    [TestClass]
    public class MC9S12XEP100HelperECCTests
    {
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private Mock<ILogger> _mockLogger;
        private MC9S12XEP100Helper _helper;

        [TestInitialize]
        public void Initialize()
        {
            _mockRegisterAccess = new Mock<IRegisterAccess>();
            _mockLogger = new Mock<ILogger>();
            _helper = new MC9S12XEP100Helper(_mockRegisterAccess.Object, _mockLogger.Object);
        }

        [TestMethod]
        public async Task EnableECCCheckingAsync_ShouldEnableECC()
        {
            // Arrange
            byte initialEccCtl = 0x00;
            byte expectedEccCtl = MC9S12XEP100Configuration.ECC.ECCCTL_ECCE;

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL))
                .ReturnsAsync(initialEccCtl);

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, expectedEccCtl))
                .ReturnsAsync(true);

            // Act
            bool result = await _helper.EnableECCCheckingAsync(true);

            // Assert
            Assert.IsTrue(result);
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, expectedEccCtl), Times.Once);
        }

        [TestMethod]
        public async Task EnableECCCheckingAsync_ShouldDisableECC()
        {
            // Arrange
            byte initialEccCtl = MC9S12XEP100Configuration.ECC.ECCCTL_ECCE;
            byte expectedEccCtl = 0x00;

            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL))
                .ReturnsAsync(initialEccCtl);

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, expectedEccCtl))
                .ReturnsAsync(true);

            // Act
            bool result = await _helper.EnableECCCheckingAsync(false);

            // Assert
            Assert.IsTrue(result);
            _mockRegisterAccess.Verify(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL), Times.Once);
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, expectedEccCtl), Times.Once);
        }

        [TestMethod]
        public async Task ReadFlashBlockAsync_WithECCCallback_ShouldEnableECCChecking()
        {
            // Arrange
            uint address = 0x700000;
            int size = 16;
            bool eccEnabled = false;

            // Setup ECC checking
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL))
                .ReturnsAsync((byte)(eccEnabled ? MC9S12XEP100Configuration.ECC.ECCCTL_ECCE : 0x00));

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, It.IsAny<byte>()))
                .Callback<uint, byte>((addr, value) => eccEnabled = (value & MC9S12XEP100Configuration.ECC.ECCCTL_ECCE) != 0)
                .ReturnsAsync(true);

            // Setup flash read
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsInRange<uint>(address, address + size, Range.Inclusive)))
                .ReturnsAsync(0xAA);

            // Setup ECC status register
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCSTAT))
                .ReturnsAsync(0x00); // No ECC errors

            // Act
            bool eccCallbackInvoked = false;
            MC9S12XEP100Helper.ECCErrorCallback callback = (addr, errorInfo) => { eccCallbackInvoked = true; };
            byte[] result = await _helper.ReadFlashBlockAsync(address, size, callback);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(size, result.Length);
            Assert.IsFalse(eccCallbackInvoked); // No errors, so callback shouldn't be invoked
            Assert.IsTrue(eccEnabled); // ECC should be enabled
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, MC9S12XEP100Configuration.ECC.ECCCTL_ECCE), Times.Once);
        }

        [TestMethod]
        public async Task ReadFlashBlockAsync_WithSingleBitError_ShouldInvokeCallback()
        {
            // Arrange
            uint address = 0x700000;
            int size = 16;
            bool eccEnabled = false;

            // Setup ECC checking
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL))
                .ReturnsAsync((byte)(eccEnabled ? MC9S12XEP100Configuration.ECC.ECCCTL_ECCE : 0x00));

            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCCTL, It.IsAny<byte>()))
                .Callback<uint, byte>((addr, value) => eccEnabled = (value & MC9S12XEP100Configuration.ECC.ECCCTL_ECCE) != 0)
                .ReturnsAsync(true);

            // Setup flash read
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsInRange<uint>(address, address + size, Range.Inclusive)))
                .ReturnsAsync(0xAA);

            // Setup ECC status register to indicate a single-bit error on the first read
            int readCount = 0;
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCSTAT))
                .Returns(() => 
                {
                    readCount++;
                    return Task.FromResult(readCount == 1 ? MC9S12XEP100Configuration.ECC.ECCSTAT_SBERR : (byte)0x00);
                });

            // Setup ECC data register (corrected data)
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCDR))
                .ReturnsAsync(0xAB); // Corrected value

            // Setup ECC error register
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.ECC.ECCERR))
                .ReturnsAsync(0x12); // Byte position 1, bit position 2

            // Act
            bool eccCallbackInvoked = false;
            MC9S12XEP100Helper.ECCErrorInfo capturedErrorInfo = null;
            MC9S12XEP100Helper.ECCErrorCallback callback = (addr, errorInfo) => 
            { 
                eccCallbackInvoked = true;
                capturedErrorInfo = errorInfo;
            };
            
            byte[] result = await _helper.ReadFlashBlockAsync(address, size, callback);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(size, result.Length);
            Assert.IsTrue(eccCallbackInvoked);
            Assert.IsNotNull(capturedErrorInfo);
            Assert.AreEqual(MC9S12XEP100Helper.ECCErrorStatus.SingleBitError, capturedErrorInfo.Status);
            Assert.AreEqual(0xAA, capturedErrorInfo.OriginalData);
            Assert.AreEqual(0xAB, capturedErrorInfo.CorrectedData);
            Assert.AreEqual(1, capturedErrorInfo.BytePosition);
            Assert.AreEqual(2, capturedErrorInfo.BitPosition);
        }
    }
}
