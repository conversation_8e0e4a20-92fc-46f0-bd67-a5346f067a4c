#!/usr/bin/env pwsh
# Script to fix PropertyChanged events in all ViewModels

Write-Host "Fixing PropertyChanged events in all ViewModels..."

# Get all ViewModel files
$viewModelFiles = Get-ChildItem -Path "VolvoFlashWR.UI\ViewModels" -Recurse -Filter "*.cs"

foreach ($file in $viewModelFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content -Path $file.FullName -Raw
    $modified = $false
    
    # Fix PropertyChangedEventHandler
    if ($content -match "public event PropertyChangedEventHandler PropertyChanged;") {
        Write-Host "  Fixing PropertyChangedEventHandler"
        $content = $content -replace "public event PropertyChangedEventHandler PropertyChanged;", "public event PropertyChangedEventHandler? PropertyChanged;"
        $modified = $true
    }
    
    # Fix EventHandler
    if ($content -match "public event EventHandler CanExecuteChanged") {
        Write-Host "  Fixing EventHandler"
        $content = $content -replace "public event EventHandler CanExecuteChanged", "public event EventHandler? CanExecuteChanged"
        $modified = $true
    }
    
    if ($modified) {
        Write-Host "  Writing updated content to file"
        Set-Content -Path $file.FullName -Value $content
    } else {
        Write-Host "  No changes needed"
    }
}

# Get all Control files
$controlFiles = Get-ChildItem -Path "VolvoFlashWR.UI\Controls" -Recurse -Filter "*.cs"

foreach ($file in $controlFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content -Path $file.FullName -Raw
    $modified = $false
    
    # Fix PropertyChangedEventHandler
    if ($content -match "public event PropertyChangedEventHandler PropertyChanged;") {
        Write-Host "  Fixing PropertyChangedEventHandler"
        $content = $content -replace "public event PropertyChangedEventHandler PropertyChanged;", "public event PropertyChangedEventHandler? PropertyChanged;"
        $modified = $true
    }
    
    if ($modified) {
        Write-Host "  Writing updated content to file"
        Set-Content -Path $file.FullName -Value $content
    } else {
        Write-Host "  No changes needed"
    }
}

Write-Host "PropertyChanged event fix completed."
