@echo off
echo Copying required libraries and running the application...

REM Set environment variables for normal mode
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true
set CONSOLE_LOGGING=true
set VOCOM_TRACE=true
set VOCOM_DETAILED_LOGGING=true
set USB_DEVICE_LOGGING=true
set DRIVER_DIAGNOSTICS=true

REM Create output directory if it doesn't exist
mkdir "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64" 2>nul

REM Copy required libraries from Libraries folder to the output directory
echo Copying required libraries...
copy "Libraries\apci.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
copy "Libraries\Volvo.ApciPlus.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
copy "Libraries\Ionic.Zip.Reduced.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
copy "Libraries\SharpCompress.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
copy "Libraries\Vodia.Contracts.Common.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul

REM Copy Volvo libraries
for %%f in (Libraries\Volvo*.dll) do (
    copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
)

REM Copy System libraries
for %%f in (Libraries\System*.dll) do (
    copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
)

REM Copy Vodia libraries
for %%f in (Libraries\Vodia*.dll) do (
    copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
)

echo Libraries copied successfully.
echo Running the application...

REM Run the application
cd VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64
VolvoFlashWR.Launcher.exe --mode Normal --verbose

echo Application has finished running.
pause
