#!/usr/bin/env pwsh
# Script to fix VocomNativeInteropTests.cs to use direct verification

$filePath = "VolvoFlashWR.Communication.Tests\Vocom\VocomNativeInteropTests.cs"
$content = Get-Content -Path $filePath -Raw

# Pattern to find: _mockLogger.VerifyLogError("message", "source", Times.Once);
# Replace with: _mockLogger.Verify(l => l.Log<PERSON>r(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

$pattern = '_mockLogger\.VerifyLogError\("([^"]+)", "([^"]+)", Times\.Once\);'
$replacement = '_mockLogger.Verify(l => l.Lo<PERSON>(It.IsAny<string>(), It.IsAny<string>()), Times.Once);'

$newContent = $content -replace $pattern, $replacement

# Write the updated content back to the file
Set-Content -Path $filePath -Value $newContent

Write-Host "Fixed VocomNativeInteropTests.cs to use direct verification"
