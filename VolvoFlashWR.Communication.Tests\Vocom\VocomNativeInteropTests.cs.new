using System;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Vocom;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Vocom
{
    [TestFixture]
    public class VocomNativeInteropTests
    {
        private Mock<ILoggingService> _mockLogger;
        private VocomNativeInterop _nativeInterop;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _nativeInterop = new VocomNativeInterop(_mockLogger.Object);
        }

        [Test]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.That(() => new VocomNativeInterop(null), Throws.TypeOf<ArgumentNullException>());
        }

        [Test]
        public void IsInitialized_BeforeInitialization_ReturnsFalse()
        {
            // Act & Assert
            Assert.That(_nativeInterop.IsInitialized, Is.False);
        }

        [Test]
        public async Task InitializeAsync_WhenDriverNotFound_ReturnsFalse()
        {
            // Arrange
            // The test environment won't have the actual driver DLL

            // Act
            bool result = await _nativeInterop.InitializeAsync();

            // Assert
            Assert.That(result, Is.False);
            Assert.That(_nativeInterop.IsInitialized, Is.False);
            _mockLogger.Verify(l => l.LogError("Failed to initialize Vocom native interop", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task DetectDevicesAsync_WhenNotInitialized_ReturnsEmptyArray()
        {
            // Act
            var devices = await _nativeInterop.DetectDevicesAsync();

            // Assert
            Assert.That(devices, Is.Not.Null);
            Assert.That(devices, Is.Empty);
            _mockLogger.Verify(l => l.LogError("Cannot detect devices: Not initialized", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task ConnectDeviceAsync_WithNullDevice_ReturnsFalse()
        {
            // Act
            bool result = await _nativeInterop.ConnectDeviceAsync(null);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot connect device: Device is null", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task ConnectDeviceAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _nativeInterop.ConnectDeviceAsync(device);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot connect device: Not initialized", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task DisconnectDeviceAsync_WithNullDevice_ReturnsFalse()
        {
            // Act
            bool result = await _nativeInterop.DisconnectDeviceAsync(null);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot disconnect device: Device is null", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task DisconnectDeviceAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            bool result = await _nativeInterop.DisconnectDeviceAsync(device);

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot disconnect device: Not initialized", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithNullDevice_ReturnsNull()
        {
            // Act
            var result = await _nativeInterop.SendCANFrameAsync(null, new byte[] { 1, 2, 3 }, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError("Cannot send CAN frame: Device is null", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithNullData_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _nativeInterop.SendCANFrameAsync(device, null, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError("Cannot send CAN frame: Data is null or empty", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WithEmptyData_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _nativeInterop.SendCANFrameAsync(device, new byte[0], 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError("Cannot send CAN frame: Data is null or empty", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task SendCANFrameAsync_WhenNotInitialized_ReturnsNull()
        {
            // Arrange
            var device = new VocomDevice
            {
                SerialNumber = "88890300",
                Name = "Test Device",
                ConnectionType = VocomConnectionType.USB
            };

            // Act
            var result = await _nativeInterop.SendCANFrameAsync(device, new byte[] { 1, 2, 3 }, 10);

            // Assert
            Assert.That(result, Is.Null);
            _mockLogger.Verify(l => l.LogError("Cannot send CAN frame: Not initialized", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task IsPTTRunningAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Act
            bool result = await _nativeInterop.IsPTTRunningAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot check if PTT is running: Not initialized", "VocomNativeInterop"), Times.Once);
        }

        [Test]
        public async Task DisconnectPTTAsync_WhenNotInitialized_ReturnsFalse()
        {
            // Act
            bool result = await _nativeInterop.DisconnectPTTAsync();

            // Assert
            Assert.That(result, Is.False);
            _mockLogger.Verify(l => l.LogError("Cannot disconnect PTT: Not initialized", "VocomNativeInterop"), Times.Once);
        }
    }
}
