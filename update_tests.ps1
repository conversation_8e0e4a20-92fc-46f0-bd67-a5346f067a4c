$testFiles = Get-ChildItem -Path "VolvoFlashWR.Communication.Tests" -Recurse -Filter "*.cs" | Where-Object { $_.FullName -like "*Tests.cs" }

Write-Host "Found $($testFiles.Count) test files"

foreach ($file in $testFiles) {
    Write-Host "Processing $($file.FullName)"
    $content = Get-Content -Path $file.FullName -Raw

    # Check if the file contains ClassicAssert
    if ($content -match "ClassicAssert") {
        Write-Host "  File contains ClassicAssert"

        # Check if the file already has the Legacy namespace
        if ($content -match "using NUnit.Framework.Legacy;") {
            Write-Host "  File already has Legacy namespace"
        } else {
            Write-Host "  Updating file to add Legacy namespace"

            # Add the Legacy namespace after the NUnit.Framework namespace
            $updatedContent = $content -replace "using NUnit.Framework;", "using NUnit.Framework;`r`nusing NUnit.Framework.Legacy;"

            # Write the updated content back to the file
            Set-Content -Path $file.FullName -Value $updatedContent
            Write-Host "  File updated"
        }
    } else {
        Write-Host "  File does not contain ClassicAssert, skipping"
    }
}

Write-Host "All test files processed."
