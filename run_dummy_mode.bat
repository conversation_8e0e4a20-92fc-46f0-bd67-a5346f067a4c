@echo off
echo Starting VolvoFlashWR application in Dummy Mode...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=true
set VERBOSE_LOGGING=true

REM Change to the Launcher project directory
cd VolvoFlashWR.Launcher

REM Run the application with explicit STA threading
dotnet run --framework net8.0-windows --property:StartupObject=VolvoFlashWR.Launcher.STAProgram

echo Application should be running. Check for a new window that has opened.
pause
