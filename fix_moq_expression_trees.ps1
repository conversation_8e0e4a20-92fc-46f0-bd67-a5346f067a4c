#!/usr/bin/env pwsh
# Script to fix Moq expression tree issues with optional arguments

Write-Host "Fixing Moq expression tree issues with optional arguments..."

# Get all test files
$testProjects = @(
    "VolvoFlashWR.Communication.Tests",
    "VolvoFlashWR.Core.Tests",
    "VolvoFlashWR.UI.Tests"
)

foreach ($project in $testProjects) {
    Write-Host "Processing project: $project"
    $testFiles = Get-ChildItem -Path $project -Recurse -Filter "*.cs" | Where-Object { $_.FullName -like "*Tests.cs" }
    
    Write-Host "Found $($testFiles.Count) test files"
    
    foreach ($file in $testFiles) {
        Write-Host "Processing $($file.FullName)"
        $content = Get-Content -Path $file.FullName -Raw
        $modified = $false
        
        # Fix expression tree issues with optional arguments in Moq setup
        # This is a common pattern: _mockService.Setup(m => m.Method(It.IsAny<string>(), It.IsAny<bool>()))
        # The issue is when the second parameter is optional and omitted in the lambda
        
        # Look for patterns like: Setup(m => m.Method(It.IsAny<Type>()))
        # where Method might have optional parameters
        if ($content -match "Setup\(.*=>.*\(It\.IsAny<.*>\(\)\)\)") {
            Write-Host "  Potential expression tree issue detected"
            
            # Replace specific patterns based on the errors we've seen
            # For example, replace:
            # _mockService.Setup(m => m.LogError(It.IsAny<string>(), It.IsAny<string>()))
            # with:
            # _mockService.Setup(m => m.LogError(It.IsAny<string>(), It.IsAny<string>(), null))
            
            # This is a simplified approach - in a real scenario, you'd need to analyze each method signature
            # and add the missing parameters explicitly
            
            # Fix for LogError method (common in our codebase)
            if ($content -match "Setup\(.*=>.*LogError\(It\.IsAny<string>\(\), It\.IsAny<string>\(\)\)\)") {
                Write-Host "  Fixing LogError method calls"
                $content = $content -replace "Setup\((.*=>.*LogError\(It\.IsAny<string>\(\), It\.IsAny<string>\(\)\)\))", "Setup(`$1, null))"
                $modified = $true
            }
            
            # Fix for other methods with optional parameters
            # This is a generic approach that might need customization based on your specific methods
            $methodsWithOptionalParams = @(
                "SendAndReceiveDataAsync",
                "ConnectAsync",
                "DisconnectAsync",
                "ScanForDevicesAsync",
                "ReadRegisterAsync",
                "WriteRegisterAsync"
            )
            
            foreach ($method in $methodsWithOptionalParams) {
                if ($content -match "Setup\(.*=>.*$method\(.*It\.IsAny<.*>\(\).*\)\)") {
                    Write-Host "  Fixing $method method calls"
                    
                    # This is a simplified approach - you might need to customize this based on the specific method signatures
                    # For example, if a method has multiple overloads with different optional parameters
                    
                    # Replace the setup with a Returns() call instead of using expression trees with optional parameters
                    $content = $content -replace "Setup\((.*=>.*$method\(.*\)\))\.Returns", "SetupSequence(`$1).Returns"
                    $modified = $true
                }
            }
        }
        
        if ($modified) {
            Write-Host "  Writing updated content to file"
            Set-Content -Path $file.FullName -Value $content
        } else {
            Write-Host "  No changes needed"
        }
    }
}

Write-Host "Moq expression tree fix completed."
