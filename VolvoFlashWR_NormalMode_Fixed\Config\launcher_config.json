{"DefaultLaunchMode": "Normal", "ShowLauncherUI": true, "RememberLastMode": true, "LastSelectedMode": "Normal", "CustomConfigPath": "", "AdditionalEnvironmentVariables": {"BACKUP_RETENTION_DAYS": "30", "MAX_LOG_SIZE_MB": "100"}, "AppConfigPath": "Config/app_config.json", "UseGraphicalUI": false, "ShowAdvancedOptions": false, "CheckForUpdates": true, "UpdateCheckUrl": "https://example.com/updates", "Logging": {"VerboseLogging": true, "LogFilePath": "Logs", "MaxLogFileSizeMB": 10, "MaxLogFileCount": 10, "LogToConsole": true, "LogToFile": true, "MinimumLogLevel": "Information"}, "Backup": {"BackupDirectoryPath": "Backups", "UseCompression": true, "UseEncryption": false, "MaxBackupsToKeep": 10, "AutomaticBackups": true, "AutomaticBackupIntervalHours": 24, "PredefinedCategories": ["Production", "Development", "Testing", "Archived", "Critical"], "PredefinedTags": ["Important", "Verified", "Debug", "Stable", "Beta", "Experimental", "Approved"]}, "UI": {"Theme": "Light", "Language": "en-US", "ShowTooltips": true, "ShowSplashScreen": true, "ConfirmExit": true, "ShowAdvancedOptions": false, "FontSize": 12}, "Communication": {"AutoConnectVocom": true, "UseWiFiFallback": false, "ConnectionTimeoutMs": 5000, "RetryAttempts": 3, "RetryDelayMs": 1000, "AutoScanECUs": true, "DefaultProtocolType": "CAN"}}