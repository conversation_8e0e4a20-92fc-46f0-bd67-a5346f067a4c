#!/usr/bin/env pwsh
# Script to fix VocomNativeInteropTests.cs to use the MoqExtensions helper

$filePath = "VolvoFlashWR.Communication.Tests\Vocom\VocomNativeInteropTests.cs"
$content = Get-Content -Path $filePath -Raw

# Pattern to find: _mockLogger.Verify(l => l.Lo<PERSON>(It.Is<string>(s => s == "message"), It.Is<string>(s => s == "source")), Times.Once);
# Replace with: _mockLogger.VerifyLogError("message", "source", Times.Once);

$pattern = '_mockLogger\.Verify\(l => l\.LogError\(It\.Is<string>\(s => s == "([^"]+)"\), It\.Is<string>\(s => s == "([^"]+)"\)\), Times\.Once\);'
$replacement = '_mockLogger.VerifyLogError("$1", "$2", Times.Once);'

$newContent = $content -replace $pattern, $replacement

# Write the updated content back to the file
Set-Content -Path $filePath -Value $newContent

Write-Host "Fixed VocomNativeInteropTests.cs to use MoqExtensions helper"
