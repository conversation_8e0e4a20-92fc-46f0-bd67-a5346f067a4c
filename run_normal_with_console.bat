@echo off
echo Starting VolvoFlashWR application in Normal Mode with console output...

REM Update the configuration file to ensure it's set to use real implementations
echo Updating configuration file...
echo {> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   "Application": {>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "ShowSplashScreen": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "OperatingMode": "Normal",>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "UseDummyImplementations": false,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "CheckForUpdates": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "AutomaticBackups": true>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   },>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   "Logging": {>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "LogToFile": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "LogFilePath": "Logs",>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "MaxLogFileCount": 10,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "DetailedLogging": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "LogToConsole": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "MinimumLogLevel": "Debug",>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "MaxLogFileSizeMB": 10>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   },>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   "Vocom": {>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "AutoDetect": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "PreferredConnectionType": "USB",>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "USBTimeoutMs": 5000,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "RetryCount": 3,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "EnableTracing": true,>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo     "DisconnectPTTBeforeConnect": true>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo   }>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"
echo }>> "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json"

REM Clear any existing environment variables first
set "USE_DUMMY_IMPLEMENTATIONS="
set "VERBOSE_LOGGING="
set "LOG_LEVEL="
set "SAFE_MODE="
set "DEMO_MODE="

REM Set environment variables for normal mode - explicitly set to "false" string
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true
set CONSOLE_LOGGING=true
set VOCOM_TRACE=true
set VOCOM_DETAILED_LOGGING=true
set USB_DEVICE_LOGGING=true
set DRIVER_DIAGNOSTICS=true

REM Copy required libraries from Libraries folder to the output directory
echo Copying required libraries...
if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\apci.dll" (
    copy "Libraries\apci.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied apci.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Volvo.ApciPlus.dll" (
    copy "Libraries\Volvo.ApciPlus.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Volvo.ApciPlus.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Ionic.Zip.Reduced.dll" (
    copy "Libraries\Ionic.Zip.Reduced.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Ionic.Zip.Reduced.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\SharpCompress.dll" (
    copy "Libraries\SharpCompress.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied SharpCompress.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Vodia.Contracts.Common.dll" (
    copy "Libraries\Vodia.Contracts.Common.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Vodia.Contracts.Common.dll
)

REM Copy Volvo libraries
for %%f in (Libraries\Volvo*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

REM Copy System libraries
for %%f in (Libraries\System*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

REM Copy Vodia libraries
for %%f in (Libraries\Vodia*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

echo.
echo Please connect your Vocom adapter via USB now if not already connected.
echo The application will start in 5 seconds...
timeout /t 5 > nul

echo.
echo Running application in normal mode with console output...
cd VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64
VolvoFlashWR.Launcher.exe --mode Normal --verbose

echo.
echo Application has finished running.
echo Check the log files in the Logs directory for more information.
pause
