# PowerShell script to run VolvoFlashWR with diagnostics
Write-Host "Starting VolvoFlashWR application with diagnostics..." -ForegroundColor Green

# Set environment variables
$env:USE_DUMMY_IMPLEMENTATIONS = "true"
$env:VERBOSE_LOGGING = "true"

# Create necessary directories
$configDir = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config"
$logsDir = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Logs"
$backupsDir = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Backups"
$vocomDir = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Drivers\Vocom"
$mcuDir = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Drivers\MC9S12XEP100"

New-Item -Path $configDir -ItemType Directory -Force | Out-Null
New-Item -Path $logsDir -ItemType Directory -Force | Out-Null
New-Item -Path $backupsDir -ItemType Directory -Force | Out-Null
New-Item -Path $vocomDir -ItemType Directory -Force | Out-Null
New-Item -Path $mcuDir -ItemType Directory -Force | Out-Null

Write-Host "Created necessary directories" -ForegroundColor Green

# Create app_config.json
$appConfigPath = "$configDir\app_config.json"
$appConfig = @{
    Application = @{
        UseDummyImplementations = $true
        OperatingMode = "Bench"
        ShowSplashScreen = $true
        CheckForUpdates = $true
        AutomaticBackups = $true
    }
    Logging = @{
        DetailedLogging = $true
        LogFilePath = "Logs"
        MaxLogFileSizeMB = 10
        MaxLogFileCount = 10
        LogToConsole = $true
        LogToFile = $true
        MinimumLogLevel = "Information"
    }
}

$appConfigJson = $appConfig | ConvertTo-Json -Depth 10
Set-Content -Path $appConfigPath -Value $appConfigJson
Write-Host "Created app_config.json" -ForegroundColor Green

# Build the solution
Write-Host "Building the solution..." -ForegroundColor Green
dotnet build VolvoFlashWR.sln --configuration Debug
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
}
Write-Host "Build completed successfully" -ForegroundColor Green

# Check if the executable exists
$exePath = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe"
if (Test-Path $exePath) {
    Write-Host "Executable found at: $exePath" -ForegroundColor Green
} else {
    Write-Host "Executable not found at: $exePath" -ForegroundColor Red
    Write-Host "Checking for executable in other locations..." -ForegroundColor Yellow
    
    $altExePath = "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\VolvoFlashWR.Launcher.exe"
    if (Test-Path $altExePath) {
        Write-Host "Executable found at alternative location: $altExePath" -ForegroundColor Green
        $exePath = $altExePath
    } else {
        Write-Host "Executable not found at alternative location: $altExePath" -ForegroundColor Red
        
        # Try to find the executable
        Write-Host "Searching for executable..." -ForegroundColor Yellow
        $foundExes = Get-ChildItem -Path "VolvoFlashWR.Launcher\bin" -Recurse -Filter "VolvoFlashWR.Launcher.exe"
        if ($foundExes.Count -gt 0) {
            Write-Host "Found $($foundExes.Count) executables:" -ForegroundColor Green
            foreach ($exe in $foundExes) {
                Write-Host "  $($exe.FullName)" -ForegroundColor Green
            }
            $exePath = $foundExes[0].FullName
            Write-Host "Using: $exePath" -ForegroundColor Green
        } else {
            Write-Host "No executables found" -ForegroundColor Red
            exit 1
        }
    }
}

# Run the application
Write-Host "Running the application..." -ForegroundColor Green
try {
    # Get the directory of the executable
    $exeDir = Split-Path -Parent $exePath
    
    # Change to the directory
    Push-Location $exeDir
    
    # Run the executable
    Write-Host "Executing: $exePath" -ForegroundColor Green
    Start-Process -FilePath (Split-Path -Leaf $exePath) -NoNewWindow
    
    # Return to the original directory
    Pop-Location
    
    Write-Host "Application should be running. Check for a new window that has opened." -ForegroundColor Green
} catch {
    Write-Host "Error running the application: $_" -ForegroundColor Red
    exit 1
}

# Wait for user input
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
