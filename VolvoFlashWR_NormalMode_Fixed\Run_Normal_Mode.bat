@echo off
echo Starting VolvoFlashWR application in Normal Mode...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set LOAD_NATIVE_LIBRARIES=true
set NATIVE_LIBRARY_PATH=.\Drivers\Vocom

REM Check if the Vocom driver is installed
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo Vocom driver found at expected location.
) else (
    echo WARNING: Vocom driver not found at expected location.
    echo Please ensure the Vocom driver (CommunicationUnitInstaller-2.5.0.0.msi) is installed.
    echo The application will continue but may not be able to connect to real Vocom devices.
    pause
)

REM Run the application in normal mode
echo Starting application...
start "" "VolvoFlashWR.Launcher.exe" --mode Normal

echo Application should be running. Check for a new window that has opened.
