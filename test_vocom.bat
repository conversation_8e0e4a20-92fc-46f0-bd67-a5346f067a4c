@echo off
echo Starting VolvoFlashWR application in Normal Mode for Vocom testing...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug

REM Create logs directory if it doesn't exist
mkdir "Logs" 2>nul

echo Checking for Vocom driver installation...
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo Vocom driver found at expected location.
) else (
    echo WARNING: Vocom driver not found at expected location.
    echo Please ensure the Vocom driver (CommunicationUnitInstaller-*******.msi) is installed.
)

echo.
echo Please connect your Vocom adapter via USB now if not already connected.
echo The application will start in 5 seconds...
timeout /t 5

echo.
echo Starting application in normal mode...
start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" --mode Normal

echo.
echo Application has been started. Please check the UI for Vocom connection status.
echo When you're done testing, close the application and press any key to exit this script.
pause
