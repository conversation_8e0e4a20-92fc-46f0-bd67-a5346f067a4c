@echo off
echo Starting VolvoFlashWR application directly...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=true
set VERBOSE_LOGGING=true

REM Find the executable
for /r VolvoFlashWR.Launcher\bin %%i in (VolvoFlashWR.Launcher.exe) do (
    echo Found executable: %%i
    echo Running: %%i
    "%%i"
    if errorlevel 1 (
        echo Error running executable with exit code: %errorlevel%
    ) else (
        echo Executable launched successfully
    )
)

echo Application should be running. Check for a new window that has opened.
pause
