@echo off
echo Copying Vocom driver files...

REM Create Drivers\Vocom directory if it doesn't exist
if not exist "Drivers\Vocom" mkdir "Drivers\Vocom"

REM Check if the driver is installed in the standard location
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo Found Vocom driver in standard installation location, copying to application directory...
    copy "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" "Drivers\Vocom\" /Y
    copy "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" ".\" /Y
    echo Copied WUDFPuma.dll from standard installation location.
) else (
    echo Vocom driver not found in standard installation location.
    echo Checking alternative locations...
    
    REM Check in Phoenix Diag folder
    if exist "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\WUDFPuma.dll" (
        echo Found Vocom driver in Phoenix Diag folder, copying to application directory...
        copy "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\WUDFPuma.dll" "Drivers\Vocom\" /Y
        copy "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\WUDFPuma.dll" ".\" /Y
        echo Copied WUDFPuma.dll from Phoenix Diag folder.
    ) else (
        echo Vocom driver not found in Phoenix Diag folder.
        echo Checking Windows driver store...
        
        REM Try to find the driver in the Windows driver store
        for /f "delims=" %%i in ('dir /s /b "C:\Windows\System32\DriverStore\FileRepository\wudfpuma.inf_*\WUDFPuma.dll" 2^>nul') do (
            echo Found Vocom driver in Windows driver store, copying to application directory...
            copy "%%i" "Drivers\Vocom\" /Y
            copy "%%i" ".\" /Y
            echo Copied WUDFPuma.dll from Windows driver store.
            goto :driver_found
        )
        
        echo Vocom driver not found in Windows driver store.
        echo WARNING: Could not find Vocom driver in any location.
        echo The application will fall back to dummy mode.
        goto :end
    )
)

:driver_found
echo.
echo Vocom driver files copied successfully.
echo.

REM Copy other required DLLs if they exist
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll" (
    copy "C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll" "Drivers\Vocom\" /Y
    echo Copied WdfCoInstaller01009.dll
)

if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll" (
    copy "C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll" "Drivers\Vocom\" /Y
    echo Copied winusbcoinstaller2.dll
)

if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll" (
    copy "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll" "Drivers\Vocom\" /Y
    echo Copied WUDFUpdate_01009.dll
)

:end
echo.
echo Driver copy process completed.
pause
