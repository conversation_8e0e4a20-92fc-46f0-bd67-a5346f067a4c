@echo off
echo Starting VolvoFlashWR application in Normal Mode with enhanced Vocom logging...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set CONSOLE_LOGGING=true
set VOCOM_TRACE=true
set VOCOM_DETAILED_LOGGING=true
set USB_DEVICE_LOGGING=true
set DRIVER_DIAGNOSTICS=true

REM Create logs directory if it doesn't exist
mkdir "Logs" 2>nul

REM Set a simple log file name
set "logfile=Logs\vocom_test.log"

echo Checking for Vocom driver installation...
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo Vocom driver found at expected location.
) else (
    echo WARNING: Vocom driver not found at expected location.
    echo Please ensure the Vocom driver (CommunicationUnitInstaller-2.5.0.0.msi) is installed.
)

echo.
echo Please connect your Vocom adapter via USB now if not already connected.
echo The application will start in 5 seconds...
timeout /t 5 > nul

echo.
echo Checking if the executable exists in the win-x64 directory...
if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" (
    echo Found executable in win-x64 directory
    echo Running with enhanced Vocom logging...

    REM Run the application with console output redirected to a log file
    echo Starting application at %time% > "%logfile%"
    "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" --mode Normal --verbose >> "%logfile%" 2>&1
) else (
    echo Executable not found in win-x64 directory
    echo Checking alternative location...

    if exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\VolvoFlashWR.Launcher.exe" (
        echo Found executable in alternative location
        echo Running with enhanced Vocom logging...

        REM Run the application with console output redirected to a log file
        echo Starting application at %time% > "%logfile%"
        "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\VolvoFlashWR.Launcher.exe" --mode Normal --verbose >> "%logfile%" 2>&1
    ) else (
        echo ERROR: Could not find the VolvoFlashWR.Launcher.exe executable.
        echo Please make sure the application is built correctly.
    )
)

echo.
echo Application has finished running. Analyzing log file...
echo.

echo --- Checking for Vocom device detection ---
findstr /i "vocom" "%logfile%" > nul
if %errorlevel% equ 0 (
    echo Found Vocom-related entries in the log:
    echo.
    findstr /i "vocom" "%logfile%"
) else (
    echo No Vocom-related entries found in the log.
)

echo.
echo --- Checking for USB device detection ---
findstr /i "usb device" "%logfile%" > nul
if %errorlevel% equ 0 (
    echo Found USB device entries in the log:
    echo.
    findstr /i "usb device" "%logfile%"
) else (
    echo No USB device entries found in the log.
)

echo.
echo --- Checking for connection status ---
findstr /i "connect" "%logfile%" > nul
if %errorlevel% equ 0 (
    echo Found connection-related entries in the log:
    echo.
    findstr /i "connect" "%logfile%"
) else (
    echo No connection-related entries found in the log.
)

echo.
echo --- Checking for errors ---
findstr /i "error fail exception" "%logfile%" > nul
if %errorlevel% equ 0 (
    echo Found error-related entries in the log:
    echo.
    findstr /i "error fail exception" "%logfile%"
) else (
    echo No error-related entries found in the log.
)

echo.
echo Full log file is available at: %logfile%
echo.
echo If the application is still running, you can check the UI for Vocom connection status.
pause
