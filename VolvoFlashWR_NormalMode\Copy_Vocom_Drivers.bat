@echo off
echo Checking for Vocom driver files...

set VOCOM_DRIVER_PATH="C:\Program Files (x86)\88890020 Adapter\UMDF"
set TARGET_PATH="Drivers\Vocom"

if exist %VOCOM_DRIVER_PATH% (
    echo Vocom driver found at %VOCOM_DRIVER_PATH%
    echo Copying driver files to %TARGET_PATH%...
    
    if exist "%VOCOM_DRIVER_PATH%\WUDFPuma.dll" (
        copy "%VOCOM_DRIVER_PATH%\WUDFPuma.dll" %TARGET_PATH%
        echo Copied WUDFPuma.dll
    ) else (
        echo WARNING: WUDFPuma.dll not found in driver directory
    )
    
    if exist "%VOCOM_DRIVER_PATH%\WdfCoInstaller01009.dll" (
        copy "%VOCOM_DRIVER_PATH%\WdfCoInstaller01009.dll" %TARGET_PATH%
        echo Copied WdfCoInstaller01009.dll
    )
    
    if exist "%VOCOM_DRIVER_PATH%\winusbcoinstaller2.dll" (
        copy "%VOCOM_DRIVER_PATH%\winusbcoinstaller2.dll" %TARGET_PATH%
        echo Copied winusbcoinstaller2.dll
    )
    
    if exist "%VOCOM_DRIVER_PATH%\WUDFUpdate_01009.dll" (
        copy "%VOCOM_DRIVER_PATH%\WUDFUpdate_01009.dll" %TARGET_PATH%
        echo Copied WUDFUpdate_01009.dll
    )
    
    echo Driver files copied successfully.
) else (
    echo Vocom driver not found at %VOCOM_DRIVER_PATH%
    echo Please install the Vocom driver (CommunicationUnitInstaller-2.5.0.0.msi) on this system.
)

echo.
echo Press any key to exit...
pause > nul
