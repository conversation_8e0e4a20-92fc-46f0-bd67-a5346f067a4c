using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using VolvoFlashWR.Communication.Interfaces;
using VolvoFlashWR.Communication.Microcontroller;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Tests.Microcontroller
{
    [TestClass]
    public class MC9S12XEP100HelperSecureFlashTests
    {
        private Mock<IRegisterAccess> _mockRegisterAccess;
        private Mock<ILogger> _mockLogger;
        private Mock<IFlashSecurity> _mockSecurity;
        private MC9S12XEP100Helper _helper;

        [TestInitialize]
        public void Initialize()
        {
            _mockRegisterAccess = new Mock<IRegisterAccess>();
            _mockLogger = new Mock<ILogger>();
            _mockSecurity = new Mock<IFlashSecurity>();
            
            // Create a helper with the mocked dependencies
            _helper = new MC9S12XEP100Helper(
                _mockRegisterAccess.Object, 
                _mockLogger.Object, 
                flashSecurity: _mockSecurity.Object);
        }

        [TestMethod]
        public async Task SecureFlashProgramAsync_ShouldEraseAndProgramFlash()
        {
            // Arrange
            uint address = 0x700000;
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A };
            uint sectorAddress = address & ~(uint)(MC9S12XEP100Configuration.SECTOR_SIZE - 1);

            // Setup sector verification (not erased)
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsInRange<uint>(sectorAddress, sectorAddress + MC9S12XEP100Configuration.SECTOR_SIZE - 1, Range.Inclusive)))
                .ReturnsAsync(0x00); // Not erased (not 0xFF)

            // Setup sector erase
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, MC9S12XEP100Configuration.FlashPerformance.CMD_ERASE_SECTOR))
                .ReturnsAsync(true);
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_HIGH, It.IsAny<byte>()))
                .ReturnsAsync(true);
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_MID, It.IsAny<byte>()))
                .ReturnsAsync(true);
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_ADDR_LOW, It.IsAny<byte>()))
                .ReturnsAsync(true);
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT, It.IsAny<byte>()))
                .ReturnsAsync(true);

            // Setup flash status register for erase completion
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_STAT))
                .ReturnsAsync(MC9S12XEP100Configuration.FlashPerformance.FSTAT_CCIF);

            // Setup flash programming
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsInRange<uint>(address, address + data.Length - 1, Range.Inclusive), It.IsAny<byte>()))
                .ReturnsAsync(true);

            // Setup flash verification
            _mockSecurity.Setup(s => s.VerifyFlashIntegrityAsync(address, data.Length))
                .ReturnsAsync(true);

            // Act
            bool result = await _helper.SecureFlashProgramAsync(address, data);

            // Assert
            Assert.IsTrue(result);
            
            // Verify sector erase was called
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, MC9S12XEP100Configuration.FlashPerformance.CMD_ERASE_SECTOR), Times.Once);
            
            // Verify flash integrity check was called
            _mockSecurity.Verify(s => s.VerifyFlashIntegrityAsync(address, data.Length), Times.Once);
        }

        [TestMethod]
        public async Task SecureFlashProgramAsync_WithAlreadyErasedSector_ShouldSkipErase()
        {
            // Arrange
            uint address = 0x700000;
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            uint sectorAddress = address & ~(uint)(MC9S12XEP100Configuration.SECTOR_SIZE - 1);

            // Setup sector verification (already erased)
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsInRange<uint>(sectorAddress, sectorAddress + MC9S12XEP100Configuration.SECTOR_SIZE - 1, Range.Inclusive)))
                .ReturnsAsync(0xFF); // Already erased

            // Setup flash programming
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsInRange<uint>(address, address + data.Length - 1, Range.Inclusive), It.IsAny<byte>()))
                .ReturnsAsync(true);

            // Setup flash verification
            _mockSecurity.Setup(s => s.VerifyFlashIntegrityAsync(address, data.Length))
                .ReturnsAsync(true);

            // Act
            bool result = await _helper.SecureFlashProgramAsync(address, data);

            // Assert
            Assert.IsTrue(result);
            
            // Verify sector erase was NOT called
            _mockRegisterAccess.Verify(r => r.WriteRegisterByteAsync(MC9S12XEP100Configuration.SPI.Registers.FLASH_CMD, MC9S12XEP100Configuration.FlashPerformance.CMD_ERASE_SECTOR), Times.Never);
            
            // Verify flash integrity check was called
            _mockSecurity.Verify(s => s.VerifyFlashIntegrityAsync(address, data.Length), Times.Once);
        }

        [TestMethod]
        public async Task SecureFlashProgramAsync_WithVerificationFailure_ShouldReturnFalse()
        {
            // Arrange
            uint address = 0x700000;
            byte[] data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
            uint sectorAddress = address & ~(uint)(MC9S12XEP100Configuration.SECTOR_SIZE - 1);

            // Setup sector verification (already erased)
            _mockRegisterAccess.Setup(r => r.ReadRegisterByteAsync(It.IsInRange<uint>(sectorAddress, sectorAddress + MC9S12XEP100Configuration.SECTOR_SIZE - 1, Range.Inclusive)))
                .ReturnsAsync(0xFF); // Already erased

            // Setup flash programming
            _mockRegisterAccess.Setup(r => r.WriteRegisterByteAsync(It.IsInRange<uint>(address, address + data.Length - 1, Range.Inclusive), It.IsAny<byte>()))
                .ReturnsAsync(true);

            // Setup flash verification to fail
            _mockSecurity.Setup(s => s.VerifyFlashIntegrityAsync(address, data.Length))
                .ReturnsAsync(false);

            // Act
            bool result = await _helper.SecureFlashProgramAsync(address, data);

            // Assert
            Assert.IsFalse(result);
            
            // Verify flash integrity check was called
            _mockSecurity.Verify(s => s.VerifyFlashIntegrityAsync(address, data.Length), Times.Once);
        }
    }
}
