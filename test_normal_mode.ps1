# Test script for VolvoFlashWR normal mode functionality
Write-Host "Starting VolvoFlashWR normal mode test script..." -ForegroundColor Green

# Check if PTT is running and terminate it if necessary
Write-Host "Checking if PTT is running..." -ForegroundColor Yellow
$pttProcesses = Get-Process -Name "ptt", "PTT", "VolvoPTT", "VolvoTechTool", "TechTool" -ErrorAction SilentlyContinue
if ($pttProcesses) {
    Write-Host "PTT process found, attempting to terminate..." -ForegroundColor Red
    $pttProcesses | ForEach-Object { 
        try {
            $_ | Stop-Process -Force
            Write-Host "Successfully terminated PTT process: $($_.Name)" -ForegroundColor Green
        } catch {
            Write-Host "Failed to terminate PTT process: $($_.Name)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "No PTT processes found, continuing..." -ForegroundColor Green
}

# Check if Vocom device is connected
Write-Host "Checking for Vocom device..." -ForegroundColor Yellow
$vocomDevice = Get-PnpDevice -FriendlyName "*Vocom*" -ErrorAction SilentlyContinue
if ($vocomDevice) {
    Write-Host "Vocom device found: $($vocomDevice.FriendlyName)" -ForegroundColor Green
} else {
    Write-Host "No Vocom device found. Please connect a Vocom device and try again." -ForegroundColor Red
    Write-Host "Continuing test anyway..." -ForegroundColor Yellow
}

# Set environment variables for normal mode
Write-Host "Setting environment variables for normal mode..." -ForegroundColor Yellow
$env:USE_DUMMY_IMPLEMENTATIONS = "false"
$env:VERBOSE_LOGGING = "true"
$env:LOG_LEVEL = "Debug"

# Build the application
Write-Host "Building the application..." -ForegroundColor Yellow
dotnet build VolvoFlashWR.sln --configuration Release

# Check if build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "Build successful!" -ForegroundColor Green

# Run the application in normal mode
Write-Host "Running the application in normal mode..." -ForegroundColor Yellow
Write-Host "The application will start in a new window. Please test the following functionality:" -ForegroundColor Cyan
Write-Host "1. Verify that the application connects to the Vocom device" -ForegroundColor Cyan
Write-Host "2. Verify that the application can scan for ECUs" -ForegroundColor Cyan
Write-Host "3. Verify that the application can read data from ECUs" -ForegroundColor Cyan
Write-Host "4. Verify that the application can write data to ECUs" -ForegroundColor Cyan
Write-Host "5. Verify that the backup functionality works" -ForegroundColor Cyan
Write-Host "6. Verify that the application can disconnect from the Vocom device" -ForegroundColor Cyan

# Start the application
Start-Process -FilePath ".\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\VolvoFlashWR.Launcher.exe" -ArgumentList "--normal-mode"

Write-Host "Test script completed. The application should now be running in normal mode." -ForegroundColor Green
Write-Host "Press any key to exit this script..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
