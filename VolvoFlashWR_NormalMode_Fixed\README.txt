VolvoFlashWR Application - Normal Mode (Fixed)
=========================================

This package contains the VolvoFlashWR application configured to run in Normal Mode for testing with real devices.

Prerequisites:
-------------
1. Vocom 1 adapter driver (CommunicationUnitInstaller-2.5.0.0.msi) must be installed
   - This installs files in 'C:\Program Files (x86)\88890020 Adapter'
   - The key DLL is WUDFPuma.dll in the UMDF subfolder

Installation:
------------
1. Copy this entire folder to your desired location on the target computer
2. Make sure the Vocom 1 adapter driver is installed on the target computer
3. Run the application using Run_Normal_Mode.bat

Important Notes:
--------------
1. This fixed package includes the necessary configuration files and driver files that were missing in the previous package
2. The application is configured to look for the Vocom driver in both the system installation path and the local Drivers/Vocom folder
3. If you encounter any issues, check the logs in the Logs folder

Usage:
-----
1. Connect your Vocom adapter to the computer via USB
2. Run the Run_Normal_Mode.bat script
3. The application will start in normal mode and attempt to connect to the Vocom adapter
4. If the connection is successful, you can proceed with ECU operations

Troubleshooting:
--------------
If the application fails to connect to the Vocom adapter:
1. Check that the Vocom adapter is properly connected via USB
2. Verify that the Vocom driver is installed correctly
3. Check the logs in the Logs folder for any error messages
4. Make sure no other application is using the Vocom adapter
5. Try running the application as administrator

Configuration:
------------
The application is pre-configured to run in normal mode with the following settings:
- UseDummyImplementations: false
- OperatingMode: Normal
- AutoConnectVocom: true
- AutoScanForECUs: true
- EnableDetailedLogging: true

You can modify these settings in the Config/app_config.json file if needed.

Support:
-------
For any issues or questions, please contact support.
