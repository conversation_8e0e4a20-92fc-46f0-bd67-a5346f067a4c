[{"Id": "dd01630a-0c98-43e3-b599-d5a66b875ebb", "Name": "Daily Backup - EMS", "Description": "Automatic daily backup at 3:00 AM", "ECUId": "f27875ef-1e09-4007-858d-c2897666503e", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 1, "Frequency": 1, "Interval": 1, "TimeOfDay": "03:00:00", "StartHour": 3, "StartMinute": 0, "DaysOfWeek": [1], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-05-14T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-05-15T03:00:00+03:00", "Category": "Automated", "Tags": ["Daily", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 7, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}, {"Id": "cd746e20-43b9-45d5-82c2-862941859b57", "Name": "Weekly Backup - EMS", "Description": "Automatic weekly backup on Sunday at 4:00 AM", "ECUId": "f27875ef-1e09-4007-858d-c2897666503e", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 2, "Frequency": 2, "Interval": 1, "TimeOfDay": "04:00:00", "StartHour": 4, "StartMinute": 0, "DaysOfWeek": [0], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-05-14T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-05-18T04:00:00+03:00", "Category": "Automated", "Tags": ["Weekly", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 4, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}]