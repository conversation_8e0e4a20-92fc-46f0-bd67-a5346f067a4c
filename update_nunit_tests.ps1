#!/usr/bin/env pwsh
# Script to update NUnit tests from classic assertion model to constraint model

Write-Host "Updating NUnit tests from classic assertion model to constraint model..."
Write-Host "Current directory: $(Get-Location)"

# Get all test files
$testProjects = @(
    "VolvoFlashWR.Communication.Tests",
    "VolvoFlashWR.Core.Tests",
    "VolvoFlashWR.UI.Tests"
)

foreach ($project in $testProjects) {
    Write-Host "Processing project: $project"
    $testFiles = Get-ChildItem -Path $project -Recurse -Filter "*.cs" | Where-Object { $_.FullName -like "*Tests.cs" }

    Write-Host "Found $($testFiles.Count) test files"

    foreach ($file in $testFiles) {
        Write-Host "Processing $($file.FullName)"
        $content = Get-Content -Path $file.FullName -Raw
        $modified = $false

        # Check if the file already has the Legacy namespace
        if ($content -match "using NUnit.Framework;" -and $content -notmatch "using NUnit.Framework.Legacy;") {
            Write-Host "  Adding Legacy namespace"
            $content = $content -replace "using NUnit.Framework;", "using NUnit.Framework;`r`nusing NUnit.Framework.Legacy;"
            $modified = $true
        }

        # Replace Assert.AreEqual with Assert.That
        if ($content -match "Assert\.AreEqual\(") {
            Write-Host "  Replacing Assert.AreEqual with Assert.That"
            $content = $content -replace "Assert\.AreEqual\(([^,]+),\s*([^)]+)\)", "Assert.That($2, Is.EqualTo($1))"
            $modified = $true
        }

        # Replace Assert.IsTrue with Assert.That
        if ($content -match "Assert\.IsTrue\(") {
            Write-Host "  Replacing Assert.IsTrue with Assert.That"
            $content = $content -replace "Assert\.IsTrue\(([^)]+)\)", "Assert.That($1, Is.True)"
            $modified = $true
        }

        # Replace Assert.IsFalse with Assert.That
        if ($content -match "Assert\.IsFalse\(") {
            Write-Host "  Replacing Assert.IsFalse with Assert.That"
            $content = $content -replace "Assert\.IsFalse\(([^)]+)\)", "Assert.That($1, Is.False)"
            $modified = $true
        }

        # Replace Assert.IsNotNull with Assert.That
        if ($content -match "Assert\.IsNotNull\(") {
            Write-Host "  Replacing Assert.IsNotNull with Assert.That"
            $content = $content -replace "Assert\.IsNotNull\(([^)]+)\)", "Assert.That($1, Is.Not.Null)"
            $modified = $true
        }

        # Replace Assert.IsNull with Assert.That
        if ($content -match "Assert\.IsNull\(") {
            Write-Host "  Replacing Assert.IsNull with Assert.That"
            $content = $content -replace "Assert\.IsNull\(([^)]+)\)", "Assert.That($1, Is.Null)"
            $modified = $true
        }

        # Replace Assert.Contains with Assert.That
        if ($content -match "Assert\.Contains\(") {
            Write-Host "  Replacing Assert.Contains with Assert.That"
            $content = $content -replace "Assert\.Contains\(([^,]+),\s*([^)]+)\)", "Assert.That($2, Does.Contain($1))"
            $modified = $true
        }

        if ($modified) {
            Write-Host "  Writing updated content to file"
            Set-Content -Path $file.FullName -Value $content
        } else {
            Write-Host "  No changes needed"
        }
    }
}

Write-Host "NUnit test update completed."
