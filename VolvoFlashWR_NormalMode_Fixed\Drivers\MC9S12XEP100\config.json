{"MicrocontrollerType": "MC9S12XEP100", "FlashSize": 1048576, "EEPROMSize": 4096, "RAMSize": 8192, "ClockFrequency": 50000000, "SupportsHighSpeedCommunication": true, "SupportsLowSpeedCommunication": true, "SupportedProtocols": ["CAN", "SPI", "SCI", "IIC"], "DefaultProtocol": "CAN", "RegisterMap": {"CAN": {"BaseAddress": "0x0140", "ControlRegister": "0x0140", "StatusRegister": "0x0141", "DataRegister": "0x0142", "BaudRateRegister": "0x0143"}, "SPI": {"BaseAddress": "0x00D8", "ControlRegister1": "0x00D8", "ControlRegister2": "0x00D9", "BaudRateRegister": "0x00DA", "StatusRegister": "0x00DB", "DataRegister": "0x00DC"}, "SCI": {"BaseAddress": "0x00C8", "BaudRateRegister": "0x00C8", "ControlRegister1": "0x00C9", "ControlRegister2": "0x00CA", "StatusRegister": "0x00CB", "DataRegister": "0x00CC"}, "IIC": {"BaseAddress": "0x00B0", "AddressRegister": "0x00B0", "FrequencyDividerRegister": "0x00B1", "ControlRegister": "0x00B2", "StatusRegister": "0x00B3", "DataIORegister": "0x00B4"}}, "FlashMemoryMap": {"StartAddress": "0x4000", "EndAddress": "0xFFFF", "SectorSize": 1024, "EraseTime": 50, "ProgramTime": 20}, "EEPROMMemoryMap": {"StartAddress": "0x0800", "EndAddress": "0x0FFF", "SectorSize": 128, "EraseTime": 10, "ProgramTime": 5}, "SecuritySettings": {"SecurityEnabled": true, "SecurityAccessDelay": 10, "MaxSecurityAttempts": 3, "SecurityTimeoutSeconds": 300}}