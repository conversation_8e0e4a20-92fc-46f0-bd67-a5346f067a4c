@echo off
echo Starting VolvoFlashWR application in Normal Mode with fixed configuration...

REM Clear any existing environment variables first
set "USE_DUMMY_IMPLEMENTATIONS="
set "VERBOSE_LOGGING="
set "LOG_LEVEL="
set "SAFE_MODE="
set "DEMO_MODE="

REM Set environment variables for normal mode - explicitly set to "false" string
set USE_DUMMY_IMPLEMENTATIONS=false
set VERBOSE_LOGGING=true
set LOG_LEVEL=Debug
set PHOENIX_VOCOM_ENABLED=true
set CONSOLE_LOGGING=true
set VOCOM_TRACE=true
set VOCOM_DETAILED_LOGGING=true
set USB_DEVICE_LOGGING=true
set DRIVER_DIAGNOSTICS=true

REM Create logs directory if it doesn't exist
mkdir "Logs" 2>nul

REM Set a simple log file name with timestamp
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (set mydate=%%a-%%b-%%c)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (set mytime=%%a%%b)
set "logfile=Logs\vocom_test_%mydate%_%mytime%.log"

echo Checking for Vocom driver installation...
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo Vocom driver found at expected location.
) else (
    echo WARNING: Vocom driver not found at expected location.
    echo Please ensure the Vocom driver (CommunicationUnitInstaller-*******.msi) is installed.
)

REM Copy required libraries from Libraries folder to the output directory
echo Copying required libraries...
if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\apci.dll" (
    copy "Libraries\apci.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied apci.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Volvo.ApciPlus.dll" (
    copy "Libraries\Volvo.ApciPlus.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Volvo.ApciPlus.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Ionic.Zip.Reduced.dll" (
    copy "Libraries\Ionic.Zip.Reduced.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Ionic.Zip.Reduced.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\SharpCompress.dll" (
    copy "Libraries\SharpCompress.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied SharpCompress.dll
)

if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Vodia.Contracts.Common.dll" (
    copy "Libraries\Vodia.Contracts.Common.dll" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
    echo Copied Vodia.Contracts.Common.dll
)

REM Copy Volvo libraries
for %%f in (Libraries\Volvo*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

REM Copy System libraries
for %%f in (Libraries\System*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

REM Copy Vodia libraries
for %%f in (Libraries\Vodia*.dll) do (
    if not exist "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\%%~nxf" (
        copy "%%f" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\" > nul
        echo Copied %%~nxf
    )
)

echo.
echo Please connect your Vocom adapter via USB now if not already connected.
echo The application will start in 5 seconds...
timeout /t 5 > nul

echo.
echo Running application in normal mode...
start "" "VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\VolvoFlashWR.Launcher.exe" --mode Normal --verbose

echo Application should be running. Check for a new window that has opened.
echo.
echo If the application is still using dummy implementations, check the log file at:
echo VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Logs\
pause
