@echo off
echo Building and running VolvoFlashWR application with explicit STA threading...

REM Clean the solution
dotnet clean VolvoFlashWR.sln

REM Build the solution
dotnet build VolvoFlashWR.sln -c Debug

REM Run the launcher with explicit STA threading
echo Starting application...
cd VolvoFlashWR.Launcher
dotnet run --framework net8.0-windows

echo Application should be running. Check for a new window that has opened.
pause
