@echo off
echo Starting VolvoFlashWR application with configuration...

REM Set environment variables
set USE_DUMMY_IMPLEMENTATIONS=true
set VERBOSE_LOGGING=true

REM Create necessary directories
mkdir VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config 2>nul
mkdir VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Logs 2>nul
mkdir VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Backups 2>nul
mkdir VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Drivers\Vocom 2>nul
mkdir VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Drivers\MC9S12XEP100 2>nul

REM Create app_config.json
echo Creating app_config.json...
echo {> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo   "Application": {>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "UseDummyImplementations": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "OperatingMode": "Bench",>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "ShowSplashScreen": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "CheckForUpdates": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "AutomaticBackups": true>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo   },>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo   "Logging": {>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "DetailedLogging": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "LogFilePath": "Logs",>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "MaxLogFileSizeMB": 10,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "MaxLogFileCount": 10,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "LogToConsole": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "LogToFile": true,>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo     "MinimumLogLevel": "Information">> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo   }>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json
echo }>> VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config\app_config.json

REM Build the solution
echo Building the solution...
dotnet build VolvoFlashWR.sln --configuration Debug

REM Run the application
echo Running the application...
cd VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64
VolvoFlashWR.Launcher.exe

echo Application should be running. Check for a new window that has opened.
pause
