using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Windows;

namespace LaunchApp
{
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                // Ensure we're running in STA mode
                if (Thread.CurrentThread.GetApartmentState() != ApartmentState.STA)
                {
                    Console.WriteLine("Warning: Thread is not in STA mode. Attempting to set STA mode...");
                    Thread.CurrentThread.SetApartmentState(ApartmentState.STA);
                }

                // Verify STA mode
                if (Thread.CurrentThread.GetApartmentState() != ApartmentState.STA)
                {
                    MessageBox.Show("The calling thread must be STA, because many UI components require this.",
                        "Threading Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Find the launcher executable
                string launcherPath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "VolvoFlashWR.Launcher",
                    "bin",
                    "Debug",
                    "net8.0-windows",
                    "VolvoFlashWR.Launcher.exe");

                if (!File.Exists(launcherPath))
                {
                    MessageBox.Show($"Could not find launcher executable at: {launcherPath}",
                        "File Not Found", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Launch the application
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = launcherPath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error launching application: {ex.Message}\n\n{ex.StackTrace}",
                    "Launch Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
